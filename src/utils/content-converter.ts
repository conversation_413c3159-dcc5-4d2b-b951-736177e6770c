// Import HTML to Markdown converter
const TurndownService = require('turndown');
import { PropertyMapper } from './property-mapping';
import { markdownToLexical, lexicalToMarkdown } from '../markdown';
import { getFrontMatterInfo } from 'obsidian';

// Content conversion utilities
export class ContentConverter {
  static htmlToMarkdown(html: string): string {
    if (!html) return '';

    // Use Turndown for proper HTML to Markdown conversion
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full'
    });

    // Add custom rule for code blocks with language detection
    turndownService.addRule('codeBlock', {
      filter: function (node: any) {
        return node.nodeName === 'PRE' && node.firstChild && node.firstChild.nodeName === 'CODE';
      },
      replacement: function (content: string, node: any) {
        const codeElement = node.firstChild;
        const className = codeElement.className || '';
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : '';

        // Clean up the content by removing extra whitespace
        const cleanContent = content.trim();

        return '\n\n```' + language + '\n' + cleanContent + '\n```\n\n';
      }
    });

    // Add custom rule for Ghost callout cards to convert back to Obsidian callouts
    turndownService.addRule('calloutCard', {
      filter: function (node: any) {
        return node.nodeName === 'DIV' &&
          node.classList &&
          node.classList.contains('kg-callout-card');
      },
      replacement: function (content: string, node: any) {
        // Extract emoji and text from the callout card
        const emojiElement = node.querySelector('.kg-callout-emoji');
        const textElement = node.querySelector('.kg-callout-text');

        if (!textElement) return content;

        const emoji = emojiElement ? emojiElement.textContent : '';
        const text = textElement.textContent || '';

        // Map emoji back to callout type
        const calloutType = ContentConverter.getCalloutTypeFromEmoji(emoji);

        return `\n\n> [!${calloutType}]\n> ${text}\n\n`;
      }
    });

    return turndownService.turndown(html);
  }

  static createFilename(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static normalizeFrontMatter(frontMatter: any): any {
    return PropertyMapper.normalizeToGhost(frontMatter);
  }

  /**
   * Validates and normalizes a primary tag value
   * @param primaryTag - The primary tag value to validate
   * @returns The normalized primary tag or null if invalid
   */
  static validateAndNormalizePrimaryTag(primaryTag: any): string | null {
    // Check if primary tag is a valid string
    if (typeof primaryTag !== 'string') {
      return null;
    }

    // Trim whitespace
    const trimmed = primaryTag.trim();

    // Check if empty after trimming
    if (trimmed.length === 0) {
      return null;
    }

    // Check reasonable length limits (Ghost has a 191 character limit for tag names)
    if (trimmed.length > 191) {
      console.warn(`Primary tag "${trimmed}" exceeds maximum length of 191 characters, truncating`);
      return trimmed.substring(0, 191).trim();
    }

    return trimmed;
  }

  static async createGhostPostData(frontMatter: any, markdownContent: string, options: any = {}): Promise<any> {
    const { status = 'draft', isUpdate = false, existingPost = null } = options;

    // Map title-cased properties to lowercase for backward compatibility
    const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);

    const slug = normalizedFrontMatter.slug || this.slugify(normalizedFrontMatter.title);
    const newStatus = normalizedFrontMatter.status || status;

    const postData: any = {
      title: normalizedFrontMatter.title,
      slug: slug,
      featured: normalizedFrontMatter.featured || false,
      status: newStatus,
      visibility: normalizedFrontMatter.visibility || 'public',
      custom_excerpt: this.generateExcerpt(normalizedFrontMatter, markdownContent)
    };

    // Handle feature_image carefully - preserve existing if not specified in frontmatter
    const frontmatterFeatureImage = normalizedFrontMatter.feature_image || normalizedFrontMatter.image;
    if (frontmatterFeatureImage) {
      // Explicitly set feature image from frontmatter
      postData.feature_image = frontmatterFeatureImage;
    } else if (isUpdate && existingPost && existingPost.feature_image) {
      // Preserve existing feature image for updates when not specified in frontmatter
      postData.feature_image = existingPost.feature_image;
    } else {
      // Only set to null for new posts or when explicitly removing
      postData.feature_image = null;
    }

    // Handle tags with primary tag support
    if (normalizedFrontMatter.tags && Array.isArray(normalizedFrontMatter.tags)) {
      let tags = [...normalizedFrontMatter.tags];

      // If primary tag is specified, ensure it's first in the array
      const primaryTag = this.validateAndNormalizePrimaryTag(normalizedFrontMatter.primary_tag);
      if (primaryTag) {
        // Remove all instances of primary tag from current positions (case-insensitive)
        tags = tags.filter(tag =>
          typeof tag === 'string' && tag.toLowerCase() !== primaryTag.toLowerCase()
        );
        // Add primary tag at the beginning
        tags.unshift(primaryTag);
      }

      postData.tags = tags;
    } else if (normalizedFrontMatter.primary_tag) {
      // Handle case where primary tag is specified but no tags array exists
      const primaryTag = this.validateAndNormalizePrimaryTag(normalizedFrontMatter.primary_tag);
      if (primaryTag) {
        postData.tags = [primaryTag];
      }
    }

    // Handle newsletter
    if (normalizedFrontMatter.newsletter && typeof normalizedFrontMatter.newsletter === 'string') {
      // Note: We store the newsletter name in frontmatter, but Ghost API expects newsletter ID
      // The actual newsletter ID resolution will be handled in the API layer
      postData.newsletter_name = normalizedFrontMatter.newsletter;
    }

    // Handle published_at date carefully
    if (isUpdate && existingPost) {
      // CRITICAL: Include the post ID and updated_at for updates
      postData.id = existingPost.id;
      postData.updated_at = existingPost.updated_at;

      // When updating an existing post
      const existingStatus = existingPost.status;
      const existingPublishedAt = existingPost.published_at;

      if (existingStatus === 'draft' && newStatus === 'published') {
        // Draft → Published transition: Set published_at to now
        postData.published_at = new Date().toISOString();
      } else if (existingPublishedAt) {
        // Post was already published: Preserve existing published_at date
        postData.published_at = existingPublishedAt;
      } else if (newStatus === 'published') {
        // Edge case: Post has no published_at but status is published
        postData.published_at = new Date().toISOString();
      }
      // For draft posts, don't set published_at (Ghost will handle it)
    } else {
      // Creating new post
      if (newStatus === 'published') {
        // New published post: Use frontmatter date or current time
        const postDate = this.parseDate(normalizedFrontMatter.published_at) ||
          this.parseDate(normalizedFrontMatter.date) ||
          new Date();
        postData.published_at = postDate.toISOString();
      }
      // For new draft posts, don't set published_at
    }

    // Generate Lexical content from markdown (no HTML generation)
    // Always generate Lexical content for both new posts and updates
    try {
      const lexicalResult = await markdownToLexical(markdownContent);
      if (lexicalResult.success && lexicalResult.data) {
        postData.lexical = JSON.stringify(lexicalResult.data);
      } else {
        console.warn('Failed to generate Lexical content:', lexicalResult.error);
        // Fallback: create simple markdown card
        postData.lexical = JSON.stringify({
          root: {
            type: 'root',
            children: [{
              type: 'markdown',
              markdown: markdownContent,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }
        });
      }
    } catch (error) {
      console.warn('Error generating Lexical content:', error);
      // Fallback: create simple markdown card
      postData.lexical = JSON.stringify({
        root: {
          type: 'root',
          children: [{
            type: 'markdown',
            markdown: markdownContent,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      });
    }

    // No HTML generation - Ghost will generate HTML from Lexical

    // Lexical content is handled above - no additional processing needed here

    // Always set mobiledoc to null (deprecated format)
    postData.mobiledoc = null;

    return postData;
  }

  static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Get callout type from emoji (for reverse conversion)
   */
  static getCalloutTypeFromEmoji(emoji: string): string {
    const emojiToType: Record<string, string> = {
      '📝': 'note',
      'ℹ️': 'info',
      '💡': 'tip',
      '✅': 'success',
      '⚠️': 'warning',
      '❌': 'danger',
      '🚨': 'error',
      '❓': 'question',
      '💬': 'quote',
      '📋': 'example',
      '📄': 'abstract',
      '☑️': 'todo',
      '🐛': 'bug'
    };

    return emojiToType[emoji] || 'note';
  }

  static generateExcerpt(frontMatter: any, content: string): string | null {
    // Always generate excerpt from content (no frontmatter excerpt support)
    if (content) {
      // Get plain text from markdown
      const plaintext = content.replace(/[#*`_\[\]()]/g, '').trim();

      if (plaintext.length <= 300) {
        return plaintext;
      }

      // Truncate at word boundary
      const truncated = plaintext.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(' ');

      if (lastSpace > 250) {
        return truncated.substring(0, lastSpace) + '...';
      }

      return truncated + '...';
    }

    return null;
  }





  /**
   * Parse article content using Obsidian's metadata cache API
   * @param content - The full file content including frontmatter
   * @param file - TFile for using Obsidian's metadata cache
   * @param app - Obsidian App instance for metadata cache access
   */
  static parseArticle(
    content: string,
    file: any,
    app: any
  ): { frontMatter: any, markdownContent: string } {
    if (!file || !app?.metadataCache) {
      throw new Error('Obsidian file and app instance are required for parsing');
    }

    const cache = app.metadataCache.getFileCache(file);
    if (!cache?.frontmatter) {
      throw new Error('No frontmatter found in file metadata cache');
    }

    // Use Obsidian's getFrontMatterInfo for precise content extraction
    const frontMatterInfo = getFrontMatterInfo(content);
    const markdownContent = frontMatterInfo.exists
      ? content.slice(frontMatterInfo.contentStart).trim()
      : content.trim();

    return {
      frontMatter: cache.frontmatter,
      markdownContent
    };
  }

  static parseMarkdown(
    content: string,
    file: any,
    app: any
  ): { frontMatter: any, markdownContent: string } {
    return this.parseArticle(content, file, app);
  }



  static objectToYaml(obj: any): string {
    let yaml = '';
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${key}:\n`;
        for (const item of value) {
          yaml += `  - ${item}\n`;
        }
      } else if (typeof value === 'string') {
        yaml += `${key}: "${value}"\n`;
      } else if (value === null) {
        // Include null values in frontmatter to show the field exists but has no value
        yaml += `${key}: null\n`;
      } else {
        yaml += `${key}: ${value}\n`;
      }
    }
    return yaml;
  }

  static async convertGhostPostToArticle(post: any): Promise<string> {
    const tags = post.tags ? post.tags.map((tag: any) => tag.name) : [];
    const createdDate = post.created_at ? new Date(post.created_at) : new Date();
    const updatedDate = post.updated_at ? new Date(post.updated_at) : new Date();
    const publishedDate = post.published_at ? new Date(post.published_at) : null;

    const ghostUpdatedTime = post.updated_at; // Use Ghost's updated_at as the content change time

    console.log('=== CONVERTING GHOST POST TO ARTICLE ===');
    console.log('ghostUpdatedTime (post.updated_at):', ghostUpdatedTime);

    // Create input object for property mapping (WITHOUT internal sync timestamps)
    const inputData = {
      title: post.title,
      slug: post.slug,
      status: post.status || 'draft',
      tags: tags,
      primary_tag: post.primary_tag?.name || null,
      visibility: post.visibility || 'public',
      feature_image: post.feature_image || null,
      featured: post.featured || false,
      newsletter: post.newsletter?.name || null,
      email_sent: post.email ? 'Yes' : 'No',
      created_at: post.created_at,
      updated_at: post.updated_at,
      published_at: post.published_at
      // Internal sync metadata (changed_at, synced_at) are stored separately, not in frontmatter
    };

    // Create frontmatter using the centralized property mapping
    const frontmatter = PropertyMapper.normalizeToObsidian(inputData);
    // Internal sync metadata is now stored separately using SyncMetadataStorage
    // No longer polluting frontmatter with internal timestamps

    let content = '';

    // MODERN GHOST → LOCAL CONVERSION: Extract markdown from lexical OR convert HTML
    if (post.lexical) {
      try {
        console.log(`✅ LEXICAL PROCESSING: Extracting content for "${post.title}"`);
        const lexicalDoc = JSON.parse(post.lexical);

        // Use the new Lexical parser for full conversion
        const conversionResult = await lexicalToMarkdown(lexicalDoc);
        if (conversionResult.success && conversionResult.data) {
          console.log(`✅ LEXICAL SUCCESS: Converted ${conversionResult.data.length} chars of markdown`);
          content = conversionResult.data;
        } else {
          console.log(`⚠️ LEXICAL PARSER FAILED: ${conversionResult.error}, trying legacy extraction`);
          // Fallback to legacy extraction
          const markdownCard = this.extractMarkdownFromLexical(lexicalDoc);
          if (markdownCard) {
            console.log(`✅ LEGACY EXTRACTION SUCCESS: Found markdown card in lexical`);
            content = markdownCard;
          } else {
            console.log(`⚠️ NO MARKDOWN CARD: Converting HTML to markdown`);
            content = this.htmlToMarkdown(post.html);
          }
        }
      } catch (error) {
        console.warn(`⚠️ LEXICAL ERROR: Failed to process lexical for "${post.title}", using HTML`);
        content = this.htmlToMarkdown(post.html);
      }
    } else if (post.html) {
      // Direct HTML → Markdown conversion
      console.log(`✅ HTML → Markdown conversion for "${post.title}"`);
      content = this.htmlToMarkdown(post.html);
    } else {
      console.error(`❌ NO CONTENT: Post "${post.title}" has no lexical or HTML content`);
      content = '';
    }

    const yamlFrontmatter = this.objectToYaml(frontmatter);
    return `---\n${yamlFrontmatter}---\n\n${content}`;
  }

  static extractMarkdownFromLexical(lexicalDoc: any): string | null {
    // Extract markdown content from lexical document structure
    try {
      if (lexicalDoc?.root?.children) {
        for (const child of lexicalDoc.root.children) {
          // Look for markdown cards
          if (child.type === 'markdown' && child.markdown) {
            return child.markdown;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to extract markdown from lexical:', error);
      return null;
    }
  }

  /**
   * DEPRECATED: This method has been removed.
   * Use SyncMetadataStorage.markAsChanged() instead to update timestamps in metadata storage.
   * Internal sync timestamps should NOT be stored in frontmatter.
   */








}
